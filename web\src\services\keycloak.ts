import keycloak, { isWebCryptoSupported, isSecureContext } from '../../config/keycloak';

export interface KeycloakService {
  login: () => Promise<void>;
  logout: () => Promise<void>;
  isAuthenticated: () => boolean;
  getToken: () => string | undefined;
  getUserInfo: () => any;
  init: (isCallback?: boolean) => Promise<boolean>;
  handleCallback: () => Promise<boolean>;
}

class KeycloakServiceImpl implements KeycloakService {
  private initialized = false;

  async init(isCallback: boolean = false): Promise<boolean> {
    if (this.initialized) {
      return keycloak.authenticated || false;
    }

    try {
      // 检查环境兼容性
      const webCryptoSupported = isWebCryptoSupported();
      const secureContext = isSecureContext();

      console.log('环境检查:', {
        webCryptoSupported,
        secureContext,
        protocol: window.location.protocol,
        hostname: window.location.hostname
      });

      let initOptions: any;

      if (isCallback) {
        // 如果是回调页面，使用不同的初始化选项
        initOptions = {
          onLoad: 'login-required',
          checkLoginIframe: false,
          // 根据环境决定是否使用PKCE
          pkceMethod: webCryptoSupported ? 'S256' : undefined,
          // 移除silentCheckSsoRedirectUri以避免iframe问题
        };
      } else {
        // 正常页面的初始化选项
        initOptions = {
          onLoad: 'check-sso',
          checkLoginIframe: false,
          // 根据环境决定是否使用PKCE
          pkceMethod: webCryptoSupported ? 'S256' : undefined,
          // 移除所有静默检查相关配置以避免超时问题
          enableLogging: true,
          // 设置较短的超时时间
          messageReceiveTimeout: 5000,
        };
      }

      console.log('Keycloak初始化选项:', initOptions);

      const authenticated = await keycloak.init(initOptions);

      this.initialized = true;

      // 设置token刷新
      if (authenticated) {
        this.setupTokenRefresh();
      }

      console.log('Keycloak初始化完成:', { authenticated, token: !!keycloak.token });

      return authenticated;
    } catch (error) {
      console.error('Keycloak初始化失败:', error);
      this.initialized = false;

      // 如果是Web Crypto API错误，提供更友好的错误信息
      if (error instanceof Error && error.message.includes('Web Crypto API')) {
        console.warn('Web Crypto API不可用，可能需要HTTPS环境或现代浏览器支持');
      }

      return false;
    }
  }

  async login(targetPath?: string): Promise<void> {
    try {
      // 检查环境兼容性
      const webCryptoSupported = isWebCryptoSupported();
      const secureContext = isSecureContext();

      if (!webCryptoSupported && !secureContext) {
        console.warn('当前环境不支持Web Crypto API，建议使用HTTPS或localhost环境');
      }

      // 确保Keycloak已初始化
      if (!this.initialized) {
        await this.init();
      }

      // 构建前端回调URI
      const frontendUrl = window.location.origin;
      const redirectParam = targetPath || '/Console/projects';
      const redirectUri = `${frontendUrl}/auth/callback`;

      console.log('登录后将重定向到:', redirectParam);
      console.log('Keycloak登录重定向URI:', redirectUri);
      console.log('环境信息:', { webCryptoSupported, secureContext });

      // 保存目标路径到 sessionStorage
      sessionStorage.setItem('auth_redirect_path', redirectParam);

      // 确保keycloak实例存在
      if (!keycloak) {
        throw new Error('Keycloak实例未初始化');
      }

      // 根据环境设置登录选项
      const loginOptions: any = {
        redirectUri: redirectUri,
        prompt: 'login'
      };

      // 如果不支持Web Crypto API，不使用PKCE
      if (!webCryptoSupported) {
        console.log('Web Crypto API不可用，使用标准授权码流程');
      }

      await keycloak.login(loginOptions);
    } catch (error) {
      console.error('Keycloak登录失败:', error);
      this.initialized = false;

      // 提供更详细的错误信息
      if (error instanceof Error) {
        if (error.message.includes('Web Crypto API')) {
          throw new Error('Web Crypto API不可用。请确保：\n1. 使用HTTPS协议访问\n2. 或在localhost环境下测试\n3. 使用支持Web Crypto API的现代浏览器');
        }
      }

      throw error;
    }
  }

  async logout(): Promise<void> {
    try {
      console.log('开始Keycloak登出...');

      // 获取当前token
      const token = this.getToken();
      
      // 如果token不存在，直接清除本地状态并跳转
      if (!token) {
        console.log('未找到有效token，直接跳转到登录页');
        keycloak.clearToken();
        window.location.replace('/#/user/login');
        return;
      }

      try {
        // 调用后端登出接口
        const response = await fetch('/api/auth/logout', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${token}`
          }
        });

        const result = await response.json();

        if (result.success && result.logoutUrl) {
          console.log('后端登出成功，重定向到Keycloak登出页面:', result.logoutUrl);
          // 清除本地token
          keycloak.clearToken();
          // 重定向到Keycloak登出页面
          window.location.href = result.logoutUrl;
        } else {
          throw new Error('后端登出接口返回异常');
        }
      } catch (error) {
        console.warn('后端登出失败，使用前端直接登出:', error);
        // 如果后端接口失败，直接使用Keycloak前端登出
        await keycloak.logout({
          redirectUri: window.location.origin + '/#/user/login',
        });
      }
    } catch (error) {
      console.error('登出过程中出现错误:', error);
      // 如果所有方法都失败，强制清除本地状态并跳转
      keycloak.clearToken();
      window.location.replace('/#/user/login');
    }
  }

  isAuthenticated(): boolean {
    return keycloak.authenticated || false;
  }

  getToken(): string | undefined {
    return keycloak.token;
  }

  getUserInfo(): any {
    if (!keycloak.authenticated) {
      return null;
    }

    try {
      return {
        id: keycloak.tokenParsed?.sub,
        username: keycloak.tokenParsed?.preferred_username,
        email: keycloak.tokenParsed?.email,
        name: keycloak.tokenParsed?.name,
        roles: keycloak.tokenParsed?.realm_access?.roles || [],
      };
    } catch (error) {
      console.error('获取用户信息失败:', error);
      return null;
    }
  }

  async handleCallback(): Promise<boolean> {
    try {
      console.log('处理Keycloak回调...');

      // 重置初始化状态，强制重新初始化
      this.initialized = false;

      // 使用login-required模式初始化，这会处理授权码
      const authenticated = await keycloak.init({
        onLoad: 'login-required',
        checkLoginIframe: false,
        silentCheckSsoRedirectUri: window.location.origin + '/silent-check-sso.html'
      });

      this.initialized = true;

      if (authenticated) {
        this.setupTokenRefresh();
        console.log('Keycloak回调处理成功');
        
        // 获取保存的重定向路径
        const redirectPath = sessionStorage.getItem('auth_redirect_path') || '/Console/projects';
        sessionStorage.removeItem('auth_redirect_path');

        // 获取token
        const token = this.getToken();
        if (!token) {
          throw new Error('未获取到token');
        }

        try {
          // 调用后端验证token并获取用户信息
          const response = await fetch('/api/auth/verify-token', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'Authorization': `Bearer ${token}`
            }
          });

          if (!response.ok) {
            throw new Error('Token验证失败');
          }

          // 直接重定向到目标页面，使用replace避免历史记录
          window.location.replace('/#' + redirectPath);
        } catch (error) {
          console.error('Token验证失败:', error);
          // 如果验证失败，重定向到登录页
          window.location.replace('/#/user/login');
          return false;
        }
      }

      return authenticated;
    } catch (error) {
      console.error('Keycloak回调处理失败:', error);
      this.initialized = false;
      // 重定向到登录页面
      window.location.replace('/#/user/login');
      return false;
    }
  }

  private setupTokenRefresh(): void {
    // 每30秒检查一次token是否需要刷新
    setInterval(() => {
      keycloak.updateToken(70).then((refreshed: boolean) => {
        if (refreshed) {
          console.log('Token已刷新');
        }
      }).catch(() => {
        console.log('Token刷新失败');
        this.logout();
      });
    }, 30000);
  }
}

export const keycloakService = new KeycloakServiceImpl();
