# Keycloak 问题排查指南

## 问题概述

本文档解决两个主要的Keycloak集成问题：

1. **初次加载页面控制台报错**：`Keycloak初始化失败: {error: 'Timeout when waiting for 3rd party check iframe message.'}`
2. **远端服务器登录报错**：`Keycloak登录失败: Error: Web Crypto API is not available.`

## 问题分析

### 问题1：iframe超时错误

**原因**：
- Keycloak默认使用iframe进行静默SSO检查
- `checkLoginIframe: false`设置与`silentCheckSsoRedirectUri`配置冲突
- `silentCheckSsoFallback: true`可能导致超时

**解决方案**：
- 完全禁用iframe检查
- 移除静默检查相关配置
- 优化初始化选项

### 问题2：Web Crypto API不可用

**原因**：
- PKCE (Proof Key for Code Exchange) 需要Web Crypto API支持
- Web Crypto API只在安全上下文（HTTPS或localhost）中可用
- 远端服务器可能使用HTTP协议

**解决方案**：
- 动态检测Web Crypto API支持
- 根据环境自动调整PKCE配置
- 提供环境诊断工具

## 修复内容

### 1. 配置文件优化 (`web/config/keycloak.ts`)

```typescript
// 动态检测Web Crypto API支持
const isWebCryptoSupported = () => {
    return typeof window !== 'undefined' && 
           window.crypto && 
           window.crypto.subtle && 
           window.isSecureContext;
};

// 根据环境动态设置PKCE
const keycloakConfig = {
    // ...其他配置
    pkceMethod: isWebCryptoSupported() ? 'S256' : undefined,
    checkLoginIframe: false,
};
```

### 2. 服务层优化 (`web/src/services/keycloak.ts`)

**主要改进**：
- 环境兼容性检查
- 动态PKCE配置
- 移除iframe相关配置
- 更好的错误处理
- 详细的日志输出

**关键修改**：
```typescript
// 初始化选项优化
const initOptions = {
    onLoad: isCallback ? 'login-required' : 'check-sso',
    checkLoginIframe: false,
    pkceMethod: webCryptoSupported ? 'S256' : undefined,
    enableLogging: true,
    messageReceiveTimeout: 5000,
};
```

### 3. 诊断工具 (`web/src/utils/keycloakDiagnostics.ts`)

新增环境诊断工具，检查：
- Web Crypto API支持
- 安全上下文
- 浏览器兼容性
- Keycloak服务器连接
- CORS配置

### 4. 登录页面增强 (`web/src/pages/user/Login/index.tsx`)

- 添加环境诊断按钮
- 改进错误提示
- 自动诊断功能
- 加载状态显示

## 使用方法

### 1. 本地开发环境

```bash
# 启动前端（localhost自动支持Web Crypto API）
cd web
npm run start:dev
```

### 2. 远端服务器部署

**选项A：配置HTTPS（推荐）**
```bash
# 使用nginx配置SSL证书
server {
    listen 443 ssl;
    server_name your-domain.com;
    ssl_certificate /path/to/cert.pem;
    ssl_certificate_key /path/to/key.pem;
    
    location / {
        proxy_pass http://localhost:3000;
    }
}
```

**选项B：HTTP环境（已自动适配）**
- 系统会自动检测环境并禁用PKCE
- 使用标准授权码流程
- 功能正常但安全性略低

### 3. 环境诊断

在登录页面点击"环境诊断"按钮，或在控制台运行：
```javascript
import { runKeycloakDiagnostics } from '@/utils/keycloakDiagnostics';
runKeycloakDiagnostics('http://your-keycloak-server:8080');
```

## 配置建议

### Keycloak服务器配置

1. **客户端设置**：
   ```
   Client ID: sulei_01
   Client Protocol: openid-connect
   Access Type: public
   Standard Flow Enabled: ON
   Direct Access Grants Enabled: ON
   ```

2. **重定向URI**：
   ```
   http://localhost:3000/auth/callback
   https://your-domain.com/auth/callback
   http://your-server-ip:3000/auth/callback
   ```

3. **Web Origins**：
   ```
   http://localhost:3000
   https://your-domain.com
   http://your-server-ip:3000
   ```

### 环境变量配置

```bash
# 服务端 (.env)
KEYCLOAK_URL=http://your-keycloak-server:8080
KEYCLOAK_REALM=dev_xh_key
KEYCLOAK_CLIENT_ID=sulei_01
KEYCLOAK_CLIENT_SECRET=your-client-secret
```

## 故障排除

### 常见问题

1. **仍然出现iframe超时**
   - 检查浏览器控制台是否有CORS错误
   - 确认Keycloak服务器可访问
   - 清除浏览器缓存和localStorage

2. **Web Crypto API错误持续**
   - 确认使用HTTPS或localhost
   - 检查浏览器版本（需要现代浏览器）
   - 查看诊断报告中的详细信息

3. **登录重定向失败**
   - 检查Keycloak客户端重定向URI配置
   - 确认域名和端口匹配
   - 查看网络请求是否被拦截

### 调试步骤

1. **开启详细日志**：
   ```javascript
   // 在浏览器控制台运行
   localStorage.setItem('keycloak-debug', 'true');
   ```

2. **查看网络请求**：
   - 打开浏览器开发者工具
   - 查看Network标签页
   - 检查Keycloak相关请求

3. **运行诊断工具**：
   - 点击登录页面的"环境诊断"按钮
   - 查看详细的环境检查报告

## 技术说明

### PKCE (Proof Key for Code Exchange)

- **用途**：增强OAuth2授权码流程的安全性
- **要求**：需要Web Crypto API支持
- **适用**：HTTPS环境或localhost
- **降级**：HTTP环境自动使用标准授权码流程

### 安全考虑

1. **生产环境**：强烈建议使用HTTPS
2. **开发环境**：localhost自动支持Web Crypto API
3. **测试环境**：可使用HTTP但需注意安全风险

## 更新日志

- **v1.0**：修复iframe超时和Web Crypto API问题
- **v1.1**：添加环境诊断工具
- **v1.2**：优化错误处理和用户体验
