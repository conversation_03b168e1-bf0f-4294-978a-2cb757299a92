@font-face {
  font-family: 'XinHe';
  src:local('XinHe'), url('./assets/font/font_xinhe.woff2') format('woff2');
}
@font-face {
  font-family: 'source serif 4';
  src:local('source serif 4'), url('./assets/font/source_serif.woff2') format('woff2');
}

html,
body,
#root {
  height: 100%;
  margin: 0;
  padding: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-feature-settings: 'cv03', 'cv04', 'cv11';
}



.colorWeak {
  filter: invert(80%);
}

.ant-layout {
  min-height: 100vh;
}

.ant-pro-sider.ant-layout-sider.ant-pro-sider-fixed {
  left: unset;
}

.ant-menu{
  font-size: 14px;
  line-height: 20px;
  font-weight: 500;
  border-radius: 8px;
  border: none;
}

/* 现代化菜单样式*/
.ant-menu-light .ant-menu-item,
.ant-menu-light .ant-menu-submenu-title {
  height: 36px;
  line-height: 36px;
  margin: 1px 12px;
  border-radius: 6px;
  padding: 0 12px;
  width: calc(100% - 24px);
}

.ant-menu-light .ant-menu-item-selected {
  font-weight: 600;
}

/* Logo区域样式 */
#root .ant-pro-layout .ant-pro-sider-logo{
  padding: 16px 12px;
  border-bottom: 1px solid #e2e8f0;
}

#root .ant-pro-layout .ant-pro-sider-logo >a >h1{
  min-height: 24px;
  font-size: 16px;
  font-weight: 700;
  color: #1f2937;
}

#root .ant-pro-layout .ant-pro-sider .ant-layout-sider-children{
  border-inline-end: 0;
}


//以下二者组合，实现菜单下边框
// .ant-menu-submenu{

//   border-bottom: 0 !important;
// }

// :where(.css-dev-only-do-not-override-cftkuk).ant-menu .ant-menu-item, :where(.css-dev-only-do-not-override-cftkuk).ant-menu .ant-menu-submenu, :where(.css-dev-only-do-not-override-cftkuk).ant-menu .ant-menu-submenu-title{
//     border-radius: 0;
//   border-bottom: 1px solid #393B3C;
// }


canvas {
  display: block;
}

body {
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

ul,
ol {
  list-style: none;
}

@media (max-width: 768px) {
  .ant-table {
    width: 100%;
    overflow-x: auto;
    &-thead > tr,
    &-tbody > tr {
      > th,
      > td {
        white-space: pre;
        > span {
          display: block;
        }
      }
    }
  }
}

#root .ant-card{
  border-radius: 8px;
  box-shadow: 0 2px 8px 0 rgba(0, 0, 0, 0.08), 0 1px 2px 0 rgba(0, 0, 0, 0.04);
  border: 1px solid #e5e7eb;
  transition: box-shadow 0.2s ease;
}

#root .ant-card:hover{
  box-shadow: 0 4px 12px 0 rgba(0, 0, 0, 0.12), 0 2px 4px 0 rgba(0, 0, 0, 0.08);
}

#root :where(.css-dev-only-do-not-override-19wldxz).ant-menu .ant-menu-item,
#root :where(.css-dev-only-do-not-override-19wldxz).ant-menu .ant-menu-submenu,
#root :where(.css-dev-only-do-not-override-19wldxz).ant-menu .ant-menu-submenu-title{
  border-radius: 6px;
  margin: 1px 12px;
  width: calc(100% - 24px);
  height: 36px;
  line-height: 36px;
  padding: 0 12px;
}

#root .ant-pro-layout .ant-pro-layout-content{
  padding-block: 24px;
  padding-inline: 24px;
  background: #f8fafc;
}

/* 页面容器样式 */
#root .ant-pro-page-container-children-content {
  margin: 0;
}

/* 统计卡片网格布局 */
#root .ant-row {
  margin-left: -12px;
  margin-right: -12px;
}

#root .ant-col {
  padding-left: 12px;
  padding-right: 12px;
}

/* Override Ant Design Sider transition background only for repo sidebar */
.repo-sider.ant-layout-sider {
  background-color: inherit !important;
}

.repo-sider .ant-layout-sider-children {
  background-color: inherit !important;
}

/* Light theme */
[data-theme="light"] .repo-sider.ant-layout-sider,
[data-theme="light"] .repo-sider .ant-layout-sider-children {
  background-color: #fff !important;
}

/* Dark theme */
[data-theme="dark"] .repo-sider.ant-layout-sider,
[data-theme="dark"] .repo-sider .ant-layout-sider-children {
  background-color: #1a1c1e !important;
}

/* Ensure the transition is smooth without dark background for repo sidebar trigger */
.repo-sider .ant-layout-sider-trigger {
  background-color: inherit !important;
}

[data-theme="light"] .repo-sider .ant-layout-sider-trigger {
  background-color: #fff !important;
}

[data-theme="dark"] .repo-sider .ant-layout-sider-trigger {
  background-color: #1a1c1e !important;
}

#root .ant-divider-horizontal{
  margin: 4px 0 24px 0;
}
#root .ant-pro-layout .ant-pro-layout-container{
  min-height: 100vh;
}