import { getTheme } from '@/components/RightContent/themeSwitcher';
import {
  AuditOutlined,
  BranchesOutlined,
  CodepenOutlined,
  FileOutlined,
  FlagOutlined,
  MenuFoldOutlined,
  MenuUnfoldOutlined,
  MergeOutlined,
  TagOutlined,
  ToolOutlined,
  HistoryOutlined,
  SettingOutlined,
  ConsoleSqlOutlined,
  DeploymentUnitOutlined,
  AppstoreOutlined,
} from '@ant-design/icons';
import type { MenuProps } from 'antd';
import { Menu } from 'antd';
import { useEffect, useState } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';

type MenuItem = Required<MenuProps>['items'][number];

interface RepoSidebarProps {
  collapsed?: boolean;
  onCollapse?: () => void;
}

const RepoSidebar: React.FC<RepoSidebarProps> = ({ collapsed = false, onCollapse }) => {
  const navigate = useNavigate();
  const location = useLocation();
  const [currentTheme, setCurrentTheme] = useState<'light' | 'dark'>(getTheme() || 'dark');

  // Monitor theme changes
  useEffect(() => {
    // Initialize theme
    setCurrentTheme(getTheme());

    // Listen for theme changes
    const observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        if (mutation.attributeName === 'data-theme') {
          setCurrentTheme(getTheme());
        }
      });
    });

    observer.observe(document.body, { attributes: true });

    return () => {
      observer.disconnect();
    };
  }, []);



  const items: MenuItem[] = [
    {
      key: '/tools/repo',
      icon: <CodepenOutlined />,
      label: '代码仓库',
    },

    {
      key: '/tools/repo/files',
      icon: <FileOutlined />,
      label: '文件管理',
    },
    {
      key: '/tools/repo/branch',
      icon: <BranchesOutlined />,
      label: '分支管理',
    },
    {
      key: '/tools/repo/commits',
      icon: <AuditOutlined />,
      label: '提交管理',
    },
    {
      key: '/tools/repo/compare',
      icon: <FlagOutlined />,
      label: '比较修订版本',
    },
    {
      key: '/tools/repo/tags',
      icon: <TagOutlined />,
      label: '标签',
    },
    {
      key: '/tools/repo/mergeRequests',
      icon: <MergeOutlined />,
      label: '合并请求',
    },
    {
      key: '/tools/repo/settings',
      icon: <SettingOutlined />,
      label: '设置',
    },
    
  ];

  const handleMenuClick = ({ key }: { key: string }) => {
    if (key.startsWith('/')) {
      navigate(key, { state: location.state });
    }
  };

  // 获取当前应该高亮的菜单项
  const getSelectedMenuKey = () => {
    const { pathname } = location;

    // 处理子路由情况
    if (pathname.startsWith('/tools/repo/compare/compare_result')) {
      return '/tools/repo/compare';
    }

    // 处理新建项目页面，高亮"仓库"菜单项
    if (pathname.startsWith('/tools/repo/newProject')) {
      return '/tools/repo';
    }

    if (pathname.startsWith('/tools/repo/commits/newMergeRequest') || pathname.startsWith('/tools/repo/newMergeRequest')) {
      return '/tools/repo/mergeRequests';
    }

    // 处理新建文件页面，高亮"文件管理"菜单项
    if (pathname.startsWith('/tools/repo/files/newFile')) {
      return '/tools/repo/files';
    }

    // 处理新建标签页面，高亮"标签"菜单项
    if (pathname.startsWith('/tools/repo/newTag')) {
      return '/tools/repo/tags';
    }

    // 处理活动页面
    if (pathname.startsWith('/tools/repo/activities')) {
      return '/tools/repo/activities';
    }

    // 默认返回当前路径
    return pathname;
  };

  // 我们不再需要这个状态，因为它在组件中没有被使用
  // 如果将来需要使用，可以取消注释
  // const [mainMenuCollapsed, setMainMenuCollapsed] = useState(false);

  // // 检测主菜单的折叠状态
  // useEffect(() => {
  //   const checkMainMenuCollapsed = () => {
  //     // 获取主菜单宽度，判断是否折叠
  //     const mainMenu = document.querySelector('.ant-pro-sider');
  //     if (mainMenu) {
  //       const width = mainMenu.clientWidth;
  //       // 如果宽度小于100px，认为主菜单已折叠
  //       setMainMenuCollapsed(width < 100);
  //     }
  //   };

  //   // 初始检查
  //   checkMainMenuCollapsed();

  //   // 监听变化
  //   const observer = new MutationObserver(checkMainMenuCollapsed);
  //   observer.observe(document.body, { attributes: true, subtree: true, childList: true });

  //   // 清理
  //   return () => observer.disconnect();
  // }, []);

  return (
    <div style={{
      height: '100%',
      display: 'flex',
      flexDirection: 'column',
      width: collapsed ? '64px' : '200px', // 确保宽度与Sider组件一致
    }}>
      <div
        style={{
          height: '64px',
          display: 'flex',
          alignItems: 'center',
          justifyContent: collapsed ? 'center' : 'space-between',
          paddingLeft: collapsed ? '0' : '24px',
          paddingRight: collapsed ? '0' : '12px',
          fontWeight: 'bold',
          fontSize: '18px',
          borderBottom: currentTheme === 'light' ? '1px solid #D4D4D7' : '1px solid #393B3C',
          borderRight: currentTheme === 'light' ? '1px solid #D4D4D7' : '1px solid #393B3C', // 移除右边框，避免重复
          backgroundColor: currentTheme === 'light' ? '#fff' : '#1a1c1e',
          color: currentTheme === 'light' ? '#1a1c1e' : '#fff',
          overflow: 'hidden',
          whiteSpace: 'nowrap',
        }}
      >
        <div style={{ display: 'flex', alignItems: 'center' }}>
          <ToolOutlined
            style={{ fontSize: collapsed ? '22px' : '20px', marginRight: collapsed ? '0' : '10px' }}
          />
          {!collapsed && 'Workbench'}
        </div>
        {/* 折叠按钮移到标题区域 */}
        {!collapsed && (
          <div
            onClick={onCollapse}
            style={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              cursor: 'pointer',
              color: currentTheme === 'light' ? '#535557' : '#C7C8C7',
              fontSize: '14px',
              padding: '4px',
              borderRadius: '4px',
            }}
          >
            <MenuFoldOutlined style={{ fontSize: '16px' }} />
          </div>
        )}
      </div>
      <div style={{ display: 'flex', flexDirection: 'column', flex: 1, overflow: 'hidden' }}>
        {/* 收起状态下显示一级菜单图标 */}
        {collapsed && (
          <div style={{
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            padding: '16px 0',
            gap: '12px',
            borderRight: currentTheme === 'light' ? '1px solid #D4D4D7' : '1px solid #393B3C',
            backgroundColor: currentTheme === 'light' ? '#fff' : '#1a1c1e',
            flex: 1,
            overflow: 'auto',
          }}>
            {/* 一级菜单图标 - 控制台 */}
            <div
              style={{
                width: '40px',
                height: '40px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                borderRadius: '6px',
                cursor: 'pointer',
                color: currentTheme === 'light' ? '#535557' : '#C7C8C7',
                backgroundColor: 'transparent',
              }}
              title="控制台"
            >
              <ConsoleSqlOutlined style={{ fontSize: '18px' }} />
            </div>

            {/* 一级菜单图标 - 工具套件 */}
            <div
              style={{
                width: '40px',
                height: '40px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                borderRadius: '6px',
                cursor: 'pointer',
                color: currentTheme === 'light' ? '#1a1c1e' : '#fff',
                backgroundColor: currentTheme === 'light' ? '#ECECEC' : '#393B3C',
                border: currentTheme === 'light' ? '1.5px solid #d8d8d8' : '1.5px solid #555',
              }}
              title="工具套件"
            >
              <ToolOutlined style={{ fontSize: '18px' }} />
            </div>

            {/* 一级菜单图标 - 应用部署 */}
            <div
              style={{
                width: '40px',
                height: '40px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                borderRadius: '6px',
                cursor: 'pointer',
                color: currentTheme === 'light' ? '#535557' : '#C7C8C7',
                backgroundColor: 'transparent',
              }}
              title="应用部署"
            >
              <DeploymentUnitOutlined style={{ fontSize: '18px' }} />
            </div>

            {/* 一级菜单图标 - 软件商店 */}
            <div
              style={{
                width: '40px',
                height: '40px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                borderRadius: '6px',
                cursor: 'pointer',
                color: currentTheme === 'light' ? '#535557' : '#C7C8C7',
                backgroundColor: 'transparent',
              }}
              title="软件商店"
            >
              <AppstoreOutlined style={{ fontSize: '18px' }} />
            </div>

            {/* 展开按钮 */}
            <div
              onClick={onCollapse}
              style={{
                width: '40px',
                height: '40px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                cursor: 'pointer',
                color: currentTheme === 'light' ? '#535557' : '#C7C8C7',
                marginTop: 'auto',
                marginBottom: '16px',
              }}
            >
              <MenuUnfoldOutlined style={{ fontSize: '16px' }} />
            </div>
          </div>
        )}

        {/* 展开状态下显示正常菜单 */}
        {!collapsed && (
          <Menu
            mode="inline"
            items={items}
            defaultOpenKeys={['control', 'development']}
            selectedKeys={[getSelectedMenuKey()]}
            onClick={handleMenuClick}
            style={{
              flex: 1,
              borderRight: currentTheme === 'light' ? '1px solid #D4D4D7' : '1px solid #393B3C',
              overflow: 'auto',
            }}
          />
        )}
      </div>
    </div>
  );
};

export default RepoSidebar;
