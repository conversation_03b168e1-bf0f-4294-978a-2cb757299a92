import Keycloak from 'keycloak-js';

// 检测是否支持Web Crypto API
const isWebCryptoSupported = () => {
    return typeof window !== 'undefined' &&
           window.crypto &&
           window.crypto.subtle &&
           window.isSecureContext;
};

// 检测是否为HTTPS环境
const isSecureContext = () => {
    return typeof window !== 'undefined' &&
           (window.location.protocol === 'https:' ||
            window.location.hostname === 'localhost' ||
            window.location.hostname === '127.0.0.1');
};

const keycloakConfig = {
    url: 'http://localhost:8080',
    realm: 'dev_xh_key',
    clientId: 'sulei_01',
    redirectUri: window.location.origin + '/auth/callback',
    silentCheckSsoRedirectUri: window.location.origin + '/silent-check-sso.html',
    // 根据环境动态设置PKCE方法
    pkceMethod: isWebCryptoSupported() ? 'S256' : undefined,
    checkLoginIframe: false,
    // 移除onLoad配置，让服务层控制
};

const keycloak = new Keycloak(keycloakConfig);

export default keycloak;
export { isWebCryptoSupported, isSecureContext };