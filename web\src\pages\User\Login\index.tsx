import { ProConfigProvider } from '@ant-design/pro-components';
import { useIntl, useModel } from '@umijs/max';
import { message, Button, Modal } from 'antd';
import { keycloakService } from '@/services/keycloak';
import { KeycloakDiagnostics } from '@/utils/keycloakDiagnostics';
import { useState } from 'react';
import './style.css';

const Page = () => {
  const intl = useIntl();
  const { initialState, setInitialState } = useModel('@@initialState');
  const [diagnosticModalVisible, setDiagnosticModalVisible] = useState(false);
  const [diagnosticReport, setDiagnosticReport] = useState<string>('');
  const [loading, setLoading] = useState(false);

  const handleKeycloakLogin = async () => {
    try {
      setLoading(true);

      // 获取URL中的重定向参数
      const urlParams = new URLSearchParams(window.location.search);
      const redirectPath = urlParams.get('redirect') || '/Console/projects';

      console.log('登录后将重定向到:', redirectPath);
      await keycloakService.login(redirectPath);
    } catch (error) {
      console.error('Keycloak登录失败:', error);

      // 根据错误类型提供不同的提示
      if (error instanceof Error && error.message.includes('Web Crypto API')) {
        message.error({
          content: 'Web Crypto API不可用，请使用HTTPS或localhost环境',
          duration: 5,
        });

        // 自动显示诊断信息
        handleDiagnostic();
      } else {
        message.error('Keycloak登录失败，请重试！');
      }
    } finally {
      setLoading(false);
    }
  };

  const handleDiagnostic = async () => {
    try {
      setDiagnosticModalVisible(true);
      setDiagnosticReport('正在诊断环境...');

      const results = await KeycloakDiagnostics.runFullDiagnostics();
      const report = KeycloakDiagnostics.generateReport(results);
      setDiagnosticReport(report);
    } catch (error) {
      setDiagnosticReport('诊断过程中发生错误: ' + error);
    }
  };

  return (
    <div className="login-container">
      <div className="login-content-left">

      </div>
      <div className="login-content-right">
        <div>
          <h1>芯合跨架构系统-DeepSeek专享套件</h1>

          <div style={{ marginBottom: '20px' }}>
            <Button
              type="primary"
              size="large"
              loading={loading}
              onClick={handleKeycloakLogin}
              className="login-button"
              style={{ width: '100%', marginBottom: '10px' }}
            >
              使用 Keycloak 登录
            </Button>

            <Button
              type="default"
              size="small"
              onClick={handleDiagnostic}
              style={{ width: '100%' }}
            >
              环境诊断
            </Button>
          </div>

          <div style={{ fontSize: '12px', color: '#666', textAlign: 'center' }}>
            如果登录遇到问题，请点击"环境诊断"查看详细信息
          </div>
        </div>
      </div>

      <Modal
        title="Keycloak环境诊断"
        open={diagnosticModalVisible}
        onCancel={() => setDiagnosticModalVisible(false)}
        footer={[
          <Button key="close" onClick={() => setDiagnosticModalVisible(false)}>
            关闭
          </Button>
        ]}
        width={600}
      >
        <pre style={{
          whiteSpace: 'pre-wrap',
          fontSize: '12px',
          maxHeight: '400px',
          overflow: 'auto',
          backgroundColor: '#f5f5f5',
          padding: '10px',
          borderRadius: '4px'
        }}>
          {diagnosticReport}
        </pre>
      </Modal>
    </div>
  );
};

export default () => {
  return (
    <ProConfigProvider dark>
      <Page />
    </ProConfigProvider>
  );
};
