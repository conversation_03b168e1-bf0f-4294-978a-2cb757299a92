const jwt = require("jsonwebtoken");
const jwksClient = require("jwks-rsa");

// JWKS客户端配置
const client = jwksClient({
  jwksUri: `${process.env.KEYCLOAK_URL}/realms/${process.env.KEYCLOAK_REALM}/protocol/openid-connect/certs`,
  cache: true,
  cacheMaxEntries: 5,
  cacheMaxAge: 600000, // 10分钟
});

// 获取签名密钥
function getKey(header, callback) {
  client.getSigningKey(header.kid, (err, key) => {
    if (err) {
      return callback(err);
    }
    const signingKey = key.getPublicKey();
    callback(null, signingKey);
  });
}

// JWT验证中间件
const verifyToken = (req, res, next) => {
  const authHeader = req.headers.authorization;

  if (!authHeader || !authHeader.startsWith("Bearer ")) {
    return res.status(401).json({
      success: false,
      message: "未提供访问令牌",
    });
  }

  const token = authHeader.substring(7); // 移除 "Bearer " 前缀

  jwt.verify(
    token,
    getKey,
    {
      audience: process.env.KEYCLOAK_CLIENT_ID,
      issuer: `${process.env.KEYCLOAK_URL}/realms/${process.env.KEYCLOAK_REALM}`,
      algorithms: ["RS256"],
    },
    (err, decoded) => {
      if (err) {
        console.error("JWT验证失败:", err.message);
        return res.status(401).json({
          success: false,
          message: "无效的访问令牌",
        });
      }

      // 将用户信息添加到请求对象
      req.user = {
        id: decoded.sub,
        username: decoded.preferred_username,
        email: decoded.email,
        name: decoded.name,
        roles: decoded.realm_access?.roles || [],
        clientRoles:
          decoded.resource_access?.[process.env.KEYCLOAK_CLIENT_ID]?.roles ||
          [],
      };

      next();
    }
  );
};

// 角色验证中间件
const requireRole = (role) => {
  return (req, res, next) => {
    if (!req.user) {
      return res.status(401).json({
        success: false,
        message: "用户未认证",
      });
    }

    if (!req.user.roles.includes(role)) {
      return res.status(403).json({
        success: false,
        message: `需要角色: ${role}`,
      });
    }

    next();
  };
};

// 检查是否有任一角色
const requireAnyRole = (roles) => {
  return (req, res, next) => {
    if (!req.user) {
      return res.status(401).json({
        success: false,
        message: "用户未认证",
      });
    }

    const hasRole = roles.some((role) => req.user.roles.includes(role));
    if (!hasRole) {
      return res.status(403).json({
        success: false,
        message: `需要以下角色之一: ${roles.join(", ")}`,
      });
    }

    next();
  };
};

// 管理员权限中间件 - 专门用于管理员路由
const requireAdmin = (req, res, next) => {
  // 定义管理员角色列表
  const adminRoles = ['admin', 'owner', 'maintainer'];

  // 检查是否有Keycloak认证信息
  if (req.kauth && req.kauth.grant && req.kauth.grant.access_token) {
    const tokenContent = req.kauth.grant.access_token.content;
    const roles = tokenContent.realm_access?.roles || [];

    // 检查是否有任一管理员角色
    const hasAdminRole = adminRoles.some(role => roles.includes(role));
    if (hasAdminRole) {
      console.log(`用户 ${tokenContent.preferred_username} 具有管理员权限，角色: ${roles.join(', ')}`);
      return next();
    }

    console.log(`用户 ${tokenContent.preferred_username} 权限不足，当前角色: ${roles.join(', ')}, 需要角色: ${adminRoles.join(', ')}`);
  }

  // 检查是否有自定义JWT认证信息
  if (req.user && req.user.roles) {
    const hasAdminRole = adminRoles.some(role => req.user.roles.includes(role));
    if (hasAdminRole) {
      // console.log(`用户 ${req.user.username} 具有管理员权限，角色: ${req.user.roles.join(', ')}`);
      return next();
    }

    console.log(`用户 ${req.user.username} 权限不足，当前角色: ${req.user.roles.join(', ')}, 需要角色: ${adminRoles.join(', ')}`);
  }

  return res.status(403).json({
    success: false,
    message: '需要管理员权限才能访问此资源',
    requiredRoles: adminRoles,
    currentRoles: req.kauth?.grant?.access_token?.content?.realm_access?.roles || req.user?.roles || []
  });
};

module.exports = {
  verifyToken,
  requireRole,
  requireAnyRole,
  requireAdmin,
};


